.main {
  max-width: 850px;
  margin-top: 90px;
  margin-bottom: 100px;
  padding-left: 30px;
  padding-right: 30px;

  /* margin-left: auto;
     margin-right auto;
     Is a trick for centering an element horizontally
     without needing a container. */
  margin-left: auto;
  margin-right: auto;
}

.back-to-orders-link {
  display: inline-block;
  margin-bottom: 30px;
}

.delivery-date {
  font-size: 25px;
  font-weight: 700;
  margin-bottom: 10px;
}

.product-info {
  margin-bottom: 3px;
}

.product-image {
  max-width: 150px;
  max-height: 150px;
  margin-top: 25px;
  margin-bottom: 50px;
}

.progress-labels-container {
  display: flex;
  justify-content: space-between;
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 15px;
}

.current-status {
  color: rgb(6, 125, 98);
}

@media (max-width: 575px) {
  .progress-labels-container {
    font-size: 16px;
  }
}

@media (max-width: 450px) {
  .progress-labels-container {
    flex-direction: column;
    margin-bottom: 5px;
  }

  .progress-label {
    margin-bottom: 3px;
  }
}

.progress-bar-container {
  height: 25px;
  width: 100%;

  border: 1px solid rgb(200, 200, 200);
  border-radius: 50px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: green;
  border-radius: 50px;
  width: 50%;
}
