body {
  font-family: <PERSON><PERSON>, <PERSON><PERSON>;
  color: rgb(33, 33, 33);
  /* The <body> element has a default margin of 8px
     on all sides. This removes the default margins. */
  margin: 0;
}

/* <p> elements have a default margin on the top
   and bottom. This removes the default margins. */
p {
  margin: 0;
}

button {
  cursor: pointer;
}

select {
  cursor: pointer;
}

input, select, button {
  font-family: Robot<PERSON>, Arial;
}

.button-primary {
  color: rgb(33, 33, 33);
  background-color: rgb(255, 216, 20);
  border: 1px solid rgb(252, 210, 0);
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(213, 217, 217, 0.5);
}

.button-primary:hover {
  background-color: rgb(247, 202, 0);
  border: 1px solid rgb(242, 194, 0);
}

.button-primary:active {
  background: rgb(255, 216, 20);
  border-color: rgb(252, 210, 0);
  box-shadow: none;
}

.button-secondary {
  color: rgb(33, 33, 33);
  background: white;
  border: 1px solid rgb(213, 217, 217);
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(213, 217, 217, 0.5);
}

.button-secondary:hover {
  background-color: rgb(247, 250, 250);
}

.button-secondary:active {
  background-color: rgb(237, 253, 255);
  box-shadow: none;
}

/* These styles will limit text to 2 lines. Anything
   beyond 2 lines will be replaced with "..."
   You can find this code by using an A.I. tool or by
   searching in Google.
   https://css-tricks.com/almanac/properties/l/line-clamp/ */
.limit-text-to-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.link-primary {
  color: rgb(1, 124, 182);
  cursor: pointer;
}

.link-primary:hover {
  color: rgb(196, 80, 0);
}

/* Styles for dropdown selectors. */
select {
  color: rgb(33, 33, 33);
  background-color: rgb(240, 240, 240);
  border: 1px solid rgb(213, 217, 217);
  border-radius: 8px;
  padding: 3px 5px;
  font-size: 15px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(213, 217, 217, 0.5);
}

select:focus,
input:focus {
  outline: 2px solid rgb(255, 153, 0);
}